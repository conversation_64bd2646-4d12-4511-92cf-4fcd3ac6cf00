<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书展活动详情 - 教师中心</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 自定义样式 */
        .detail-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
        }
        
        .info-item {
            transition: all 0.2s ease;
        }
        
        .info-item:hover {
            background-color: #f8fafc;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 0.75rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        /* 状态标签样式 */
        .status-draft {
            background-color: rgba(156, 163, 175, 0.2);
            color: #4b5563;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }
        .status-published {
            background-color: rgba(34, 197, 94, 0.2);
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        .status-cancelled {
            background-color: rgba(239, 68, 68, 0.2);
            color: #b91c1c;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .status-ended {
            background-color: rgba(107, 114, 128, 0.2);
            color: #1f2937;
            border: 1px solid rgba(107, 114, 128, 0.3);
        }
        .status-pending-review {
            background-color: rgba(251, 191, 36, 0.2);
            color: #92400e;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }
        .status-rejected {
            background-color: rgba(239, 68, 68, 0.2);
            color: #b91c1c;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .status-registerable {
            background-color: rgba(59, 130, 246, 0.2);
            color: #1e40af;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .status-registration-closed {
            background-color: rgba(156, 163, 175, 0.2);
            color: #4b5563;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }

        /* 富文本内容样式 */
        .exhibition-description {
            line-height: 1.6;
        }
        
        .exhibition-description img {
            width: 22%; 
            height: auto;
            display: block;
            margin: 0.5em auto;
            border-radius: 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .exhibition-description h1 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 1rem 0 0.75rem 0;
            color: #1f2937;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }
        
        .exhibition-description h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0.875rem 0 0.5rem 0;
            color: #374151;
        }
        
        .exhibition-description h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #4b5563;
        }
        
        .exhibition-description h4, .exhibition-description h5, .exhibition-description h6 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0.75rem 0 0.5rem 0;
            color: #6b7280;
        }
        
        .exhibition-description p {
            margin: 0.5rem 0;
            color: #374151;
        }
        
        .exhibition-description ul, .exhibition-description ol {
            margin: 0.5rem 0;
            padding-left: 1.25rem;
            color: #374151;
        }
        
        .exhibition-description li {
            margin: 0.25rem 0;
        }
        
        .exhibition-description strong {
            font-weight: 600;
            color: #1f2937;
        }
        
        .exhibition-description em {
            font-style: italic;
            color: #4b5563;
        }
        
        .exhibition-description a {
            color: #3b82f6;
            text-decoration: underline;
        }
        
        .exhibition-description blockquote {
            border-left: 3px solid #e5e7eb;
            padding-left: 0.75rem;
            margin: 0.75rem 0;
            font-style: italic;
            color: #6b7280;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2"></div>
    
    <!-- 返回按钮 -->
    <div id="topBackButton" class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <button onclick="history.back()" class="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回书展管理
            </button>
        </div>
    </div>

    <!-- 加载状态 -->
    <div id="loadingState" class="flex items-center justify-center min-h-[60vh]">
        <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <i class="fas fa-spinner fa-spin text-2xl text-blue-600"></i>
            </div>
            <p class="text-gray-600">加载中，请稍候...</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <div id="mainContent" class="hidden max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- 书展标题和状态 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex flex-wrap gap-2 mb-4" id="statusBadges">
                <!-- 状态标签将在这里动态插入 -->
            </div>
            <h1 id="exhibitionTitle" class="text-2xl font-bold text-gray-900 mb-4"></h1>
            <div id="exhibitionLogo" class="hidden flex justify-center mb-4">
                <img id="logoImage" alt="活动Logo" class="object-contain max-h-32 border rounded p-1">
            </div>
        </div>

        <!-- 基本信息 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                基本信息
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">发起学校:</span>
                        <span id="schoolName" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">活动地点:</span>
                        <span id="location" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">学校地址:</span>
                        <span id="schoolAddress" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">允许停车:</span>
                        <span id="allowsParking" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg" id="licensePlateInfo" style="display: none;">
                        <span class="text-gray-500 font-medium">需要车牌号:</span>
                        <span id="licensePlateRequired" class="text-gray-800 ml-2"></span>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">开始时间:</span>
                        <span id="startTime" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">结束时间:</span>
                        <span id="endTime" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">报名截止:</span>
                        <span id="registrationDeadline" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg" id="coOrganizerInfo" style="display: none;">
                        <span class="text-gray-500 font-medium">协办方:</span>
                        <span id="coOrganizerName" class="text-gray-800 ml-2"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 联系人信息 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-user text-blue-600 mr-2"></i>
                联系人信息
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-3">
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">姓名:</span>
                        <span id="contactName" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg">
                        <span class="text-gray-500 font-medium">电话:</span>
                        <span id="contactPhone" class="text-gray-800 ml-2"></span>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="info-item p-3 rounded-lg" id="contactDepartmentInfo" style="display: none;">
                        <span class="text-gray-500 font-medium">部门:</span>
                        <span id="contactDepartment" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg" id="contactPositionInfo" style="display: none;">
                        <span class="text-gray-500 font-medium">职务:</span>
                        <span id="contactPosition" class="text-gray-800 ml-2"></span>
                    </div>
                    <div class="info-item p-3 rounded-lg" id="contactEmailInfo" style="display: none;">
                        <span class="text-gray-500 font-medium">邮箱:</span>
                        <span id="contactEmail" class="text-gray-800 ml-2"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活动详情 -->
        <div id="descriptionSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-file-alt text-blue-600 mr-2"></i>
                活动详情
            </h2>
            <div id="exhibitionDescription" class="exhibition-description prose prose-gray max-w-none">
                <!-- 活动描述内容 -->
            </div>
        </div>

        <!-- 进校报备信息 -->
        <div id="registrationSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-clipboard-check text-blue-600 mr-2"></i>
                进校报备信息
            </h2>
            <div id="registrationRequirements" class="text-gray-800 whitespace-pre-line mb-4"></div>
            <div id="registrationQrcode" class="flex justify-center" style="display: none;">
                <img id="qrcodeImage" alt="报备二维码" class="max-h-48 object-contain border rounded p-2">
            </div>
        </div>

        <!-- 其他要求 -->
        <div id="requirementsSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6" style="display: none;">
            <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-list-ul text-blue-600 mr-2"></i>
                其他要求
            </h2>
            <div id="requirements" class="text-gray-800 whitespace-pre-line"></div>
        </div>

        <!-- 参展单位信息 -->
        <div id="registrationsSection" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6 relative" style="display: none;">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-building text-blue-600 mr-2"></i>
                    参展单位 <span id="registrationsCount" class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"></span>
                </h2>
                <button id="exportBtn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                    <i class="fas fa-download mr-2"></i>
                    导出Excel
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参展人数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报名时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="registrationsTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 参展单位数据将在这里动态插入 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div id="actionButtons" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex flex-wrap gap-3 justify-end">
                <!-- 返回按钮（默认显示，协办方审核时隐藏） -->
                <button id="backButton" onclick="history.back()" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-xl hover:bg-gray-300 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>返回
                </button>
                <!-- 编辑按钮（发起者可见） -->
                <button id="editButton" class="btn-primary px-6 py-3 text-white rounded-xl" style="display: none;">
                    <i class="fas fa-edit mr-2"></i>编辑书展
                </button>
                <!-- 审批按钮（协办方可见） -->
                <button id="reviewButton" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-xl transition-colors" style="display: none;">
                    <i class="fas fa-gavel mr-2"></i>审核书展
                </button>
            </div>
        </div>
    </div>

    <!-- 参展人员模态框 -->
    <div id="participantsModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 id="participantsModalTitle" class="text-lg font-semibold text-gray-900">参展人员详情</h3>
                <button class="modal-close-btn text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="participantsModalBody" class="p-6 max-h-[70vh] overflow-y-auto">
                <!-- 参展人员内容将在这里动态插入 -->
            </div>
            <div class="px-6 py-4 bg-gray-50 flex justify-end rounded-b-lg">
                <button id="participantsModalCloseBtn" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">关闭</button>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div id="reviewModalContainer" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-gavel text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-slate-800">审核书展</h3>
                            <p class="text-sm text-slate-500">请仔细审核后做出决定</p>
                        </div>
                    </div>
                    <button id="closeReviewModalBtn" class="w-8 h-8 bg-white hover:bg-slate-50 text-slate-600 rounded-lg flex items-center justify-center transition-colors shadow-sm">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-slate-700 mb-1">书展名称</label>
                        <p id="reviewExhibitionTitle" class="font-medium text-slate-900">加载中...</p>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-slate-700 mb-2">审核意见</label>
                        <textarea id="reviewComment" rows="4" placeholder="请输入审核意见（可选）" class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"></textarea>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex gap-3">
                        <button id="approveBtn" class="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-xl font-medium transition-colors flex items-center justify-center">
                            <i class="fas fa-check mr-2"></i>
                            <span>审核通过</span>
                        </button>
                        <button id="rejectBtn" class="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-xl font-medium transition-colors flex items-center justify-center">
                            <i class="fas fa-times mr-2"></i>
                            <span>审核拒绝</span>
                        </button>
                        <button id="cancelReviewBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-xl font-medium transition-colors">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let exhibitionId = null;
        let exhibitionData = null;
        let userRole = null;
        let isCoOrganizerReviewer = false;

        // 页面初始化
        $(document).ready(function() {
            // 获取URL参数中的书展ID
            const urlParams = new URLSearchParams(window.location.search);
            exhibitionId = urlParams.get('id');

            if (!exhibitionId) {
                showMessage('缺少书展ID参数', 'error');
                return;
            }

            // 检查用户权限，完成后加载书展详情
            checkUserPermission();
        });

        // 检查用户权限
        function checkUserPermission() {
            $.ajax({
                url: '/api/common/check_user_session',
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        userRole = response.data.role;
                        isCoOrganizerReviewer = response.data.has_permission;
                        console.log('用户角色:', userRole, '协办方权限:', isCoOrganizerReviewer);

                        // 权限检查完成后加载书展详情
                        loadExhibitionDetail();
                    } else {
                        console.error('获取用户权限失败:', response.message);
                        // 即使权限检查失败也要加载详情
                        loadExhibitionDetail();
                    }
                },
                error: function() {
                    console.error('获取用户权限失败');
                    // 即使权限检查失败也要加载详情
                    loadExhibitionDetail();
                }
            });
        }

        // 加载书展详情
        function loadExhibitionDetail() {
            $.ajax({
                url: '/api/common/get_exhibition_detail',
                type: 'GET',
                data: { id: exhibitionId },
                success: function(response) {
                    if (response.code === 0) {
                        exhibitionData = response.data;
                        renderExhibitionDetail(exhibitionData);
                        $('#loadingState').addClass('hidden');
                        $('#mainContent').removeClass('hidden');
                    } else {
                        showMessage(response.message || '获取详情失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }

        // 渲染书展详情
        function renderExhibitionDetail(exhibition) {
            console.log('书展详情数据:', exhibition); // 调试信息

            // 设置标题
            $('#exhibitionTitle').text(exhibition.title);

            // 设置状态标签
            const statusInfo = getStatusInfo(exhibition.status);
            const registrationStatus = getRegistrationStatus(exhibition);

            let statusHtml = `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
            statusHtml += `<span class="status-badge ${registrationStatus.class}">${registrationStatus.text}</span>`;
            $('#statusBadges').html(statusHtml);

            // 设置Logo
            if (exhibition.logo_url) {
                $('#logoImage').attr('src', exhibition.logo_url);
                $('#exhibitionLogo').removeClass('hidden');
            }

            // 设置基本信息
            $('#schoolName').text(exhibition.school_name || '未知');
            $('#location').text(exhibition.location || '未知');
            $('#schoolAddress').text(exhibition.school_address || '未提供');
            $('#startTime').text(exhibition.start_time || '未设置');
            $('#endTime').text(exhibition.end_time || '未设置');
            $('#registrationDeadline').text(exhibition.registration_deadline || '未设置');
            $('#allowsParking').text(exhibition.allows_parking ? '是' : '否');

            // 车牌信息
            if (exhibition.allows_parking) {
                $('#licensePlateRequired').text(exhibition.license_plate_required ? '是' : '否');
                $('#licensePlateInfo').show();
            }

            // 协办方信息
            if (exhibition.co_organizer_name) {
                $('#coOrganizerName').text(exhibition.co_organizer_name);
                $('#coOrganizerInfo').show();
            }

            // 设置联系人信息
            if (exhibition.initiator) {
                $('#contactName').text(exhibition.initiator.name || '未知');
                $('#contactPhone').text(exhibition.initiator.phone || '未知');

                if (exhibition.initiator.department) {
                    $('#contactDepartment').text(exhibition.initiator.department);
                    $('#contactDepartmentInfo').show();
                }

                if (exhibition.initiator.position) {
                    $('#contactPosition').text(exhibition.initiator.position);
                    $('#contactPositionInfo').show();
                }

                if (exhibition.initiator.email) {
                    $('#contactEmail').text(exhibition.initiator.email);
                    $('#contactEmailInfo').show();
                }
            }

            // 设置活动详情
            if (exhibition.description) {
                $('#exhibitionDescription').html(exhibition.description);
                $('#descriptionSection').show();
            }

            // 设置进校报备信息
            if (exhibition.requires_campus_registration) {
                if (exhibition.registration_requirements) {
                    $('#registrationRequirements').text(exhibition.registration_requirements);
                }

                if (exhibition.registration_qrcode) {
                    $('#qrcodeImage').attr('src', exhibition.registration_qrcode);
                    $('#registrationQrcode').show();
                }

                $('#registrationSection').show();
            }

            // 设置其他要求
            if (exhibition.requirements) {
                $('#requirements').text(exhibition.requirements);
                $('#requirementsSection').show();
            }

            // 设置参展单位信息
            if (exhibition.is_initiator) {
                // 对于书展发起人，始终显示参展单位区域
                if (exhibition.registrations && exhibition.registrations.length > 0) {
                    $('#registrationsCount').text(exhibition.registrations.length);
                    renderRegistrations(exhibition.registrations);
                } else {
                    $('#registrationsCount').text('0');
                    $('#registrationsTableBody').html(`
                        <tr>
                            <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                                <i class="fas fa-building text-3xl mb-3 block"></i>
                                暂无参展单位报名
                            </td>
                        </tr>
                    `);
                }
                $('#registrationsSection').show();
            }

            // 设置按钮显示逻辑
            setupButtonsDisplay(exhibition);
        }

        // 设置按钮显示逻辑
        function setupButtonsDisplay(exhibition) {
            // 直接使用后端返回的协办方权限信息
            const isCoOrganizerForThisExhibition = exhibition.is_co_organizer_reviewer || false;

            console.log('协办方权限判断结果:', {
                isCoOrganizerReviewer,
                exhibitionStatus: exhibition.status,
                coOrganizerType: exhibition.co_organizer_type,
                userRole,
                isCoOrganizerForThisExhibition,
                backendPermission: exhibition.is_co_organizer_reviewer
            });

            if (isCoOrganizerForThisExhibition) {
                // 协办方审核模式：隐藏返回按钮，显示审核按钮
                $('#topBackButton').hide();
                $('#backButton').hide();
                $('#reviewButton').show().off('click').click(function() {
                    openReviewModal(exhibition);
                });
            } else {
                // 普通查看模式：显示返回按钮
                $('#topBackButton').show();
                $('#backButton').show();
                $('#reviewButton').hide();

                // 发起者可以编辑
                if (exhibition.is_initiator) {
                    $('#editButton').show().off('click').click(function() {
                        window.location.href = `/teacher/exhibitions/edit?id=${exhibition.id}`;
                    });

                    // 绑定导出按钮事件
                    $('#exportBtn').off('click').click(function() {
                        exportRegistrations();
                    });
                }
            }
        }



        // 渲染参展单位列表
        function renderRegistrations(registrations) {
            let html = '';

            registrations.forEach(reg => {
                html += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${reg.company_name || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${reg.participants_count || 0}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="inline-flex px-2 py-1 text-xs rounded-full ${reg.status === 'registered' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${reg.status === 'registered' ? '已报名' : '已取消'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${reg.created_at || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <button class="view-participants-btn text-blue-600 hover:text-blue-800" data-id="${reg.id}">
                                <i class="fas fa-users mr-1"></i>查看人员
                            </button>
                        </td>
                    </tr>
                `;
            });

            $('#registrationsTableBody').html(html);

            // 绑定查看参展人员按钮点击事件
            $('.view-participants-btn').click(function() {
                const regId = $(this).data('id');
                viewParticipants(regId);
            });
        }

        // 查看参展人员
        function viewParticipants(registrationId) {
            $.ajax({
                url: '/api/teacher/get_exhibition_participants',
                type: 'GET',
                data: { registration_id: registrationId },
                success: function(response) {
                    if (response.code === 0) {
                        renderParticipants(response.data);
                        $('#participantsModalContainer').removeClass('hidden');
                    } else {
                        showMessage(response.message || '获取参展人员失败', 'error');
                    }
                },
                error: function() {
                    showMessage('网络错误，请稍后重试', 'error');
                }
            });
        }

        // 渲染参展人员
        function renderParticipants(participants) {
            if (!participants || participants.length === 0) {
                $('#participantsModalBody').html(`
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-users text-3xl mb-3"></i>
                        <p>暂无参展人员信息</p>
                    </div>
                `);
                return;
            }

            // 格式化字段值，未填写的显示标签
            function formatField(value, fieldName) {
                if (!value || value.trim() === '') {
                    return `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-500">
                        <i class="fas fa-minus mr-1"></i>未填写
                    </span>`;
                }
                return `<span class="text-gray-800">${value}</span>`;
            }

            let html = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务/角色</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">电话</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车牌号</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
            `;

            participants.forEach(participant => {
                html += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.name, '姓名')}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.role || participant.position, '职务')}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.phone, '电话')}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">${formatField(participant.license_plate, '车牌号')}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            $('#participantsModalBody').html(html);
        }

        // 导出参展单位和人员信息
        function exportRegistrations() {
            if (!exhibitionData || !exhibitionId) {
                showMessage('书展信息不完整，无法导出', 'error');
                return;
            }

            console.log('开始导出，书展ID:', exhibitionId); // 调试信息

            // 显示加载状态
            $('#exportBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-2"></i>导出中...');

            // 使用原生XMLHttpRequest处理blob响应
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/teacher/export_exhibition_registrations', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.responseType = 'blob';

            xhr.onload = function() {
                console.log('请求完成，状态:', xhr.status); // 调试信息

                if (xhr.status === 200) {
                    // 检查响应是否为blob
                    if (xhr.response instanceof Blob) {
                        console.log('收到blob响应，大小:', xhr.response.size); // 调试信息

                        // 创建下载链接
                        const url = window.URL.createObjectURL(xhr.response);
                        const a = document.createElement('a');
                        a.href = url;

                        // 生成文件名
                        let filename = `${exhibitionData.title}_参展单位信息.xlsx`;
                        const disposition = xhr.getResponseHeader('Content-Disposition');
                        if (disposition && disposition.indexOf('filename=') !== -1) {
                            filename = disposition.split('filename=')[1].replace(/"/g, '');
                        }

                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);

                        showMessage('导出成功！', 'success');
                    } else {
                        console.error('响应不是blob类型:', xhr.response);
                        showMessage('导出失败：响应格式错误', 'error');
                    }
                } else {
                    console.error('请求失败，状态码:', xhr.status);

                    // 尝试解析错误信息
                    if (xhr.response instanceof Blob) {
                        const reader = new FileReader();
                        reader.onload = function() {
                            try {
                                const errorData = JSON.parse(reader.result);
                                showMessage(errorData.message || '导出失败', 'error');
                            } catch (e) {
                                showMessage('导出失败，请稍后重试', 'error');
                            }
                        };
                        reader.readAsText(xhr.response);
                    } else {
                        showMessage('导出失败，请稍后重试', 'error');
                    }
                }

                // 恢复按钮状态
                $('#exportBtn').prop('disabled', false).html('<i class="fas fa-download mr-2"></i>导出Excel');
            };

            xhr.onerror = function() {
                console.error('网络错误');
                showMessage('网络错误，请检查网络连接', 'error');
                $('#exportBtn').prop('disabled', false).html('<i class="fas fa-download mr-2"></i>导出Excel');
            };

            xhr.ontimeout = function() {
                console.error('请求超时');
                showMessage('请求超时，请稍后重试', 'error');
                $('#exportBtn').prop('disabled', false).html('<i class="fas fa-download mr-2"></i>导出Excel');
            };

            // 设置超时时间为30秒
            xhr.timeout = 30000;

            // 发送请求
            const formData = `exhibition_id=${encodeURIComponent(exhibitionId)}`;
            console.log('发送请求数据:', formData); // 调试信息
            xhr.send(formData);
        }

        // 获取状态信息
        function getStatusInfo(status) {
            const statusMap = {
                'draft': { text: '未发布', class: 'status-draft' },
                'pending_review': { text: '待审核', class: 'status-pending-review' },
                'rejected': { text: '审核拒绝', class: 'status-rejected' },
                'published': { text: '已发布', class: 'status-published' },
                'cancelled': { text: '已取消', class: 'status-cancelled' },
                'ended': { text: '已结束', class: 'status-ended' }
            };
            return statusMap[status] || { text: '未知状态', class: 'status-draft' };
        }

        // 获取报名状态
        function getRegistrationStatus(exhibition) {
            const now = new Date();
            const deadline = new Date(exhibition.registration_deadline);
            const startTime = new Date(exhibition.start_time);

            if (exhibition.status === 'cancelled') {
                return { text: '活动已取消', class: 'status-cancelled' };
            }

            if (exhibition.status === 'ended' || now > new Date(exhibition.end_time)) {
                return { text: '活动已结束', class: 'status-ended' };
            }

            if (now > deadline) {
                return { text: '报名已截止', class: 'status-registration-closed' };
            }

            if (exhibition.status === 'published') {
                return { text: '可以报名', class: 'status-registerable' };
            }

            return { text: '暂未开放', class: 'status-draft' };
        }

        // 打开审核模态框
        function openReviewModal(exhibition) {
            $('#reviewExhibitionTitle').text(exhibition.title);
            $('#reviewComment').val('');
            $('#reviewModalContainer').removeClass('hidden');
        }

        // 关闭审核模态框
        function closeReviewModal() {
            $('#reviewModalContainer').addClass('hidden');
            $('#reviewComment').val('');
        }

        // 提交审核
        function submitReview(action) {
            const comment = $('#reviewComment').val().trim();

            // 禁用按钮防止重复提交
            $('#approveBtn, #rejectBtn').prop('disabled', true);

            const requestData = {
                exhibition_id: exhibitionId,
                action: action,
                comment: comment
            };

            fetch('/api/common/review_exhibition', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => response.json())
            .then(result => {
                if (result.code === 0) {
                    showMessage(result.message, 'success');
                    closeReviewModal();
                    // 重新加载页面数据
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showMessage(result.message || '审核失败', 'error');
                }
            })
            .catch(error => {
                console.error('审核失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
            })
            .finally(() => {
                // 重新启用按钮
                $('#approveBtn, #rejectBtn').prop('disabled', false);
            });
        }

        // 绑定审核模态框事件
        $(document).ready(function() {
            // 关闭模态框
            $('#closeReviewModalBtn, #cancelReviewBtn').click(function() {
                closeReviewModal();
            });

            // 审核通过
            $('#approveBtn').click(function() {
                submitReview('approve');
            });

            // 审核拒绝
            $('#rejectBtn').click(function() {
                submitReview('reject');
            });

            // 点击模态框外部关闭
            $('#reviewModalContainer').click(function(e) {
                if (e.target === this) {
                    closeReviewModal();
                }
            });
        });

        // 显示消息
        function showMessage(message, type = 'info') {
            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'error' ? 'bg-red-500' :
                           type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';

            const messageHtml = `
                <div class="message-toast ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-2 animate-fadeIn">
                    <div class="flex items-center">
                        <i class="fas ${type === 'success' ? 'fa-check-circle' :
                                      type === 'error' ? 'fa-exclamation-circle' :
                                      type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'} mr-2"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `;

            $('#messageContainer').append(messageHtml);

            // 3秒后自动移除
            setTimeout(() => {
                $('#messageContainer .message-toast:first').addClass('animate-fadeOut');
                setTimeout(() => {
                    $('#messageContainer .message-toast:first').remove();
                }, 300);
            }, 3000);
        }

        // 模态框关闭事件
        $(document).on('click', '.modal-close-btn, #participantsModalCloseBtn', function() {
            $('#participantsModalContainer').addClass('hidden');
        });

        // 点击模态框背景关闭
        $(document).on('click', '#participantsModalContainer', function(e) {
            if (e.target === this) {
                $(this).addClass('hidden');
            }
        });

        // ESC键关闭模态框
        $(document).keydown(function(e) {
            if (e.keyCode === 27) {
                $('#participantsModalContainer').addClass('hidden');
            }
        });
    </script>
</body>
</html>
